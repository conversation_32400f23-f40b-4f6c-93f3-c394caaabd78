package router

import (
	"github.com/gin-gonic/gin"
	"resc-ms-order/app/controllers"
	"resc-ms-order/router/middleware"
)

// PayRouter 定义订单相关的路由
func PayRouter(r *gin.Engine) *gin.Engine {
	// 初始化控制器
	payController := controllers.NewPayController()
	priceListController := controllers.NewPriceListController()
	refundController := controllers.NewRefundController()

	// 定义订单相关的路由组
	payRoutes := r.Group("/pay")

	// 需要管理员权限的路由
	adminRoutes := payRoutes.Group("")
	adminRoutes.Use(middleware.CheckAdmin())
	{
		// 拒绝退款,只有管理员可以操作
		adminRoutes.POST("/refund/reject", refundController.RejectRefund)
	}

	// 需要管理员或用户权限的路由
	adminOrUserRoutes := payRoutes.Group("")
	adminOrUserRoutes.Use(middleware.CheckAdminOrUser())
	{
		// 获取支付订单列表
		adminOrUserRoutes.GET("", payController.ListPayOrders)
		// 获取订单支付信息
		adminOrUserRoutes.GET("/info", payController.GetOrderPayInfo)
		// 获取退款订单列表
		adminOrUserRoutes.GET("/refund", refundController.ListRefundOrders)
		// 获取退款订单信息
		adminOrUserRoutes.GET("/refund/info", refundController.GetRefundOrderInfo)
	}

	// 需要用户权限的路由
	userRoutes := payRoutes.Group("")
	userRoutes.Use(middleware.CheckUser())
	{
		// 创建充值订单
		userRoutes.POST("", payController.CreateRechargeOrder)
		// 创建微信支付分订单
		userRoutes.POST("/wechat/score", payController.CreateWechatPayScoreOrder)
	}

	// 内部服务调用
	serverRoutes := payRoutes.Group("")
	serverRoutes.Use(middleware.CheckServer())
	{
		// 申请退款,因为需要在旧项目/用户服务中扣余额,所以禁止用户直接调用
		serverRoutes.POST("/refund", refundController.RefundOrder)
		// 取消退款,因为需要在旧项目/用户服务中加余额,所以禁止用户直接调用
		serverRoutes.POST("/refund/cancel", refundController.CancelRefund)
	}

	// 不用验证权限
	// 获取价格列表
	payRoutes.GET("/price", priceListController.ListPriceLists)
	// 微信回调接口
	payRoutes.POST("/wechat/notify", payController.WechatNotify)

	return r
}
