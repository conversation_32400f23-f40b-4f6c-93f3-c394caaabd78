# 第一阶段：构建可执行文件
FROM golang:1.23.4-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY go.mod go.sum ./

# 配置go代理环境变量
ENV GOPROXY=https://goproxy.cn,direct

# 下载依赖
RUN go mod download

# 复制项目文件
COPY . .

# 构建可执行文件
RUN CGO_ENABLED=0 GOOS=linux go build -ldflags="-s -w" -o /app/main .

# 第二阶段：生成生产环境镜像
FROM alpine:3.19

# 将Alpine的软件源切换为阿里云镜像，加快国内访问速度
RUN set -eux; \
    sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories; \
    apk update;

# 安装时区数据
RUN apk add --no-cache tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 从构建阶段复制可执行文件
COPY --from=builder /app/main /app/main

# 设置容器启动命令
CMD ["/app/main"]