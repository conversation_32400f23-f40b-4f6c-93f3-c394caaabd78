package services

import (
	"errors"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"resc-ms-order/app/models"
	"resc-ms-order/app/utils"
	"resc-ms-order/database"
	"strconv"
	"time"
)

// RefundService 订单服务结构体
type RefundService struct{}

// NewRefundService 创建订单服务实例
func NewRefundService() *RefundService {
	return &RefundService{}
}

// 退款状态常量
const (
	RefundStateApplied   = int8(1)  // 已申请
	RefundStateRefunding = int8(5)  // 退款中
	RefundStateRejected  = int8(10) // 已拒绝
	RefundStateCancelled = int8(11) // 已取消
	RefundStateSuccess   = int8(20) // 已退款
	RefundStateFailed    = int8(30) // 退款失败
	RefundStateOther     = int8(40) // 其它
)

// 退款状态描述
var RefundStateDescription = map[int8]string{
	RefundStateApplied:   "已申请",
	RefundStateRefunding: "退款中",
	RefundStateRejected:  "已拒绝",
	RefundStateCancelled: "已取消",
	RefundStateSuccess:   "已退款",
	RefundStateFailed:    "退款失败",
	RefundStateOther:     "其它",
}

// ListRefundOrders 获取退款订单列表
func (s *RefundService) ListRefundOrders(page, pageSize int, conditions []utils.QueryCondition) ([]models.Refund, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize
	refund := &models.Refund{}

	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, refund)
	list, err := refund.ListRefundOrders(database.DB, offset, pageSize, whereConditions)
	if err != nil {
		log.Error("获取退款订单失败: ", err)
		return nil, err
	}
	return list, nil
}

// CountRefundOrders 获取退款订单数量
func (s *RefundService) CountRefundOrders(conditions []utils.QueryCondition) (int64, error) {
	refund := &models.Refund{}

	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, refund)
	count, err := refund.CountRefundOrders(database.DB, whereConditions)
	if err != nil {
		log.Error("获取退款订单数量失败: ", err)
		return 0, err
	}
	return count, nil
}

// GetRefundOrder 根据ID获取退款订单记录
func (s *RefundService) GetRefundOrder(id string, conditions []utils.QueryCondition) (*models.Refund, error) {
	refund := &models.Refund{}
	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, refund)
	err := refund.GetRefundOrderByID(database.DB, id, whereConditions)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error("获取退款订单失败: ", err)
		}
		return nil, err
	}
	return refund, nil
}

// GetRefundOrderByOrderID 根据支付订单ID获取退款订单记录
func (s *RefundService) GetRefundOrderByOrderID(id string, conditions []utils.QueryCondition) (*models.Refund, error) {
	refund := &models.Refund{}
	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, refund)
	err := refund.GetRefundOrderByOrderID(database.DB, id, whereConditions)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error("获取退款订单失败: ", err)
		}
		return nil, err
	}
	return refund, nil
}

// RefundOrder 申请退款
func (s *RefundService) RefundOrder(user *models.User, orderId string) (*models.Pay, error) {
	// 防重复申请缓存锁定
	err := s.refundCacheLock("refund", user.UserID)

	// 验证支付订单是否符合退款条件
	pay, err := s.validForRefund(orderId, user)
	if err != nil {
		return nil, err
	}

	return pay, nil
}

// CancelRefund 取消退款
func (s *RefundService) CancelRefund(refundId string, user *models.User) (*models.Refund, error) {
	// 防重复操作缓存锁定
	err := s.refundCacheLock("cancel_refund", user.UserID)
	if err != nil {
		return nil, err
	}

	// 验证是否符合取消退款条件
	refundOrder, err := s.validForCancelRefund(refundId, user.UserID)
	if err != nil {
		return nil, err
	}

	// 开启事务
	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 更新退款订单状态
		err = refundOrder.UpdateRefundOrderState(tx, refundId, user.UserID, models.RefundOrderStateCancelled, models.RefundOrderStateApplied, "用户主动取消退款")
		if err != nil {
			return err
		}

		//  更新支付订单状态
		pay := &models.Pay{}
		err = pay.UpdateRefundState(tx, refundOrder.OrderID, user.UserID, models.RefundStateCancelled, []int8{models.RefundStateApplied})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return refundOrder, nil
}

// RejectRefund 拒绝退款
func (s *RefundService) RejectRefund(refundId string) (*models.Refund, error) {
	// 防重复操作缓存锁定
	err := s.refundCacheLockWithRefundId("reject_refund", refundId)
	if err != nil {
		return nil, err
	}

	// 验证是否符合拒绝退款条件
	refundOrder, err := s.validForRejectRefund(refundId)
	if err != nil {
		return nil, err
	}

	// 开启事务
	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 更新退款订单状态为已拒绝
		err = refundOrder.UpdateRefundOrderState(tx, refundId, refundOrder.UserID, models.RefundOrderStateRejected, models.RefundOrderStateApplied, "管理员拒绝退款")
		if err != nil {
			log.Error("更新退款订单状态失败: ", err)
			return errors.New("更新退款订单状态失败")
		}

		// 更新支付订单退款状态为已拒绝
		pay := &models.Pay{}
		err = pay.UpdateRefundState(tx, refundOrder.OrderID, refundOrder.UserID, models.RefundStateRejected, []int8{models.RefundStateApplied})
		if err != nil {
			log.Error("更新支付订单退款状态失败: ", err)
			return errors.New("更新支付订单退款状态失败")
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	// 发送到队列
	message := map[string]interface{}{
		"action": "refund_reject", // 拒绝退款操作
		"data":   refundOrder,
	}
	err = utils.Publish(utils.PublishParams{
		Exchange:   "order",
		QueueName:  "order_pay",
		RoutingKey: "order.pay",
		Message:    message,
		Mode:       "direct",
		Durable:    true,
	})
	if err != nil {
		log.Error("发送加余额消息到队列失败: ", err)
		return nil, errors.New("发送加余额消息到队列失败")
	}

	return refundOrder, nil
}

// validForRejectRefund 验证是否符合拒绝退款条件
func (s *RefundService) validForRejectRefund(refundID string) (*models.Refund, error) {
	// 获取退款订单
	refundOrder, err := s.GetRefundOrder(refundID, []utils.QueryCondition{})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("退款订单不存在")
		}
		log.Error("获取退款订单失败: ", err)
		return nil, errors.New("获取退款订单失败")
	}

	// 检查退款订单状态，只有已申请状态才能拒绝
	if refundOrder.State != models.RefundOrderStateApplied {
		return nil, errors.New("该笔退款正在处理或已处理，不能拒绝")
	}

	return refundOrder, nil
}

// validForRefund 验证支付订单是否符合退款条件
func (s *RefundService) validForRefund(orderID string, user *models.User) (*models.Pay, error) {
	// 获取支付订单
	conditions := []utils.QueryCondition{
		{Field: "user_id", Operator: "=", Value: user.UserID},
	}
	payService := NewPayService()
	payOrder, err := payService.GetPayOrderByID(orderID, conditions)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("订单不存在")
		}
		log.Error("获取支付订单失败: ", err)
		return nil, errors.New("获取订单信息失败")
	}

	oneYearAgo := time.Now().AddDate(-1, 0, 0)
	if payOrder.CreateTime.Time.Before(oneYearAgo) {
		return nil, errors.New("超过一年，无法退款")
	}
	if payOrder.State != models.StatePayComplete {
		return nil, errors.New("订单没支付")
	}
	if payOrder.AddBalance != models.AddBalanceSuccess {
		return nil, errors.New("订单没有加余额，请联系客服处理")
	}

	// 检查退款状态，只有无退款、已拒绝、已取消的订单才能申请退款
	allowedRefundStates := []int8{
		models.RefundStateNone,      // 无退款
		models.RefundStateRejected,  // 已拒绝
		models.RefundStateCancelled, // 已取消
	}
	isAllowed := false
	for _, state := range allowedRefundStates {
		if payOrder.RefundState == state {
			isAllowed = true
			break
		}
	}
	if !isAllowed {
		return nil, errors.New("订单已申请过退款，无法重复申请，如有疑问，请联系客服处理")
	}

	// 检查是否有充电中的订单
	orderService := NewOrderService()
	count, _ := orderService.CountOrders([]utils.QueryCondition{
		{Field: "user_id", Operator: "=", Value: user.UserID},
		{Field: "status", Operator: "in", Value: []int{1, 3, 8}},
	})
	if count > 0 {
		return nil, errors.New("您还有充电中的订单，请结束充电后再退款")
	}

	return payOrder, nil
}

// validForCancelRefund 验证是否符合取消退款条件
func (s *RefundService) validForCancelRefund(refundID string, userID int64) (*models.Refund, error) {
	// 验证退款订单
	var refundOrder *models.Refund
	conditions := []utils.QueryCondition{
		{Field: "user_id", Operator: "=", Value: userID},
	}
	refundOrder, err := s.GetRefundOrder(refundID, conditions)
	if err != nil {
		log.Error("获取退款订单失败: ", err)
		return nil, errors.New("获取退款订单失败")
	}
	if refundOrder.State != models.RefundOrderStateApplied {
		return nil, errors.New("该笔退款正在处理或已处理，不能取消，如有疑问，请联系客服处理")
	}

	payService := NewPayService()
	// 验证支付订单
	pay, err := payService.GetPayOrderByID(refundOrder.OrderID, []utils.QueryCondition{
		{Field: "user_id", Operator: "=", Value: userID},
	})
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("充值订单不存在")
		}
		log.Error("获取支付订单失败: ", err)
		return nil, errors.New("获取支付订单失败")
	}

	// 检查支付订单退款状态，只有申请退款状态才能取消
	if pay.RefundState != models.RefundStateApplied {
		return nil, errors.New("该笔退款正在处理或已处理，不能取消，如有疑问，请联系客服处理")
	}

	return refundOrder, nil
}

// 退款缓存锁定
func (s *RefundService) refundCacheLock(key string, userId int64) error {
	var cacheKey string
	var lockDuration time.Duration
	var errorMsg string

	// 根据操作类型设置不同的缓存键和锁定时间
	if key == "reject_refund" {
		// 管理员拒绝退款操作，使用退款ID作为锁定键，锁定4秒
		cacheKey = "后台退款操作_" + key
		lockDuration = 4 * time.Second
		errorMsg = "同一退款订单，请勿频繁操作"
	} else {
		// 用户操作，使用用户ID作为锁定键，锁定10秒
		cacheKey = key + "_" + strconv.FormatInt(userId, 10)
		lockDuration = 10 * time.Second
		errorMsg = "请勿频繁申请退款"
	}

	cache := utils.NewCache()

	// 检查缓存是否存在
	exists, err := cache.Exists(cacheKey)
	if err != nil {
		log.Error("检查缓存失败: ", err)
		return errors.New("系统异常，请稍后重试")
	}
	if exists {
		return errors.New(errorMsg)
	}

	// 设置缓存锁定
	err = cache.Set(cacheKey, 1, lockDuration)
	if err != nil {
		log.Error("设置缓存失败: ", err)
		return errors.New("系统异常，请稍后重试")
	}

	return nil
}

// refundCacheLockWithRefundId 使用退款ID进行缓存锁定（用于管理员操作）
func (s *RefundService) refundCacheLockWithRefundId(key string, refundId string) error {
	cacheKey := "后台退款操作_" + refundId
	cache := utils.NewCache()

	// 检查缓存是否存在
	exists, err := cache.Exists(cacheKey)
	if err != nil {
		log.Error("检查缓存失败: ", err)
		return errors.New("系统异常，请稍后重试")
	}
	if exists {
		return errors.New("同一退款订单，请勿频繁操作")
	}

	// 设置4秒缓存锁定
	err = cache.Set(cacheKey, 1, 4*time.Second)
	if err != nil {
		log.Error("设置缓存失败: ", err)
		return errors.New("系统异常，请稍后重试")
	}

	return nil
}
