package services

import (
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	log "github.com/sirupsen/logrus"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"gorm.io/gorm"
	"net/http"
	"resc-ms-order/app/models"
	"resc-ms-order/app/utils"
	"resc-ms-order/database"
)

// 微信支付交易状态常量
const (
	TradeStateSuccess    = "SUCCESS"    // 支付成功
	TradeStateRefund     = "REFUND"     // 转入退款
	TradeStateNotPay     = "NOTPAY"     // 未支付
	TradeStateClosed     = "CLOSED"     // 已关闭
	TradeStateRevoked    = "REVOKED"    // 已撤销
	TradeStateUserPaying = "USERPAYING" // 用户支付中
	TradeStatePayError   = "PAYERROR"   // 支付失败
)

// TradeStateDescription 微信支付交易状态描述
var TradeStateDescription = map[string]string{
	TradeStateSuccess:    "支付成功",
	TradeStateRefund:     "转入退款",
	TradeStateNotPay:     "未支付",
	TradeStateClosed:     "已关闭",
	TradeStateRevoked:    "已撤销",
	TradeStateUserPaying: "用户支付中",
	TradeStatePayError:   "支付失败",
}

// PayService 订单服务结构体
type PayService struct{}

// NewPayService 创建订单服务实例
func NewPayService() *PayService {
	return &PayService{}
}

// ListPayOrders 获取支付订单列表
func (s *PayService) ListPayOrders(page, pageSize int, conditions []utils.QueryCondition) ([]models.Pay, error) {
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10
	}
	offset := (page - 1) * pageSize
	pay := &models.Pay{}

	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, pay)
	list, err := pay.ListPayOrders(database.DB, offset, pageSize, whereConditions)
	if err != nil {
		log.Error("获取订单支付信息失败: ", err)
		return nil, err
	}
	return list, nil
}

// GetPayOrderByID 根据ID获取支付订单记录
func (s *PayService) GetPayOrderByID(id string, conditions []utils.QueryCondition) (*models.Pay, error) {
	pay := &models.Pay{}
	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, pay)
	err := pay.GetPayOrderByID(database.DB, id, whereConditions)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Error("获取订单支付信息失败: ", err)
		}
		return nil, err
	}
	return pay, err
}

// CountPayOrders 获取支付订单数量
func (s *PayService) CountPayOrders(conditions []utils.QueryCondition) (int64, error) {
	pay := &models.Pay{}

	// 将查询条件转换为 GORM 可用的格式
	whereConditions := utils.ConvertToWhereConditions(conditions, pay)

	return pay.CountPayOrders(database.DB, whereConditions)
}

// CreateRechargeOrderParams 创建充值订单的参数结构体
type CreateRechargeOrderParams struct {
	UserID      int64  // 用户ID
	Phone       string // 手机号码
	OpenID      string // 微信openid
	PriceID     int    // 价格列表ID
	CustomPrice int64  // 自定义金额（可选）
	IP          string // IP地址
}

// CreateRechargeOrder 处理创建充值订单
func (s *PayService) CreateRechargeOrder(ctx *gin.Context, params CreateRechargeOrderParams) (map[string]string, int, error) {
	// 参数验证
	if params.PriceID == 1 && params.CustomPrice == 0 {
		return nil, http.StatusBadRequest, errors.New("自定义支付金额不能为空")
	}

	// 创建充值订单参数
	payParams := PayOrderParams{
		UserID:      params.UserID,
		Phone:       params.Phone, // 手机号码
		ListID:      params.PriceID,
		IP:          params.IP,
		Type:        models.OrderTypeRecharge, // 充值余额
		CustomPrice: params.CustomPrice,
	}

	// 创建充值订单
	orderPay, err := s.CreatePayOrder(payParams)
	if err != nil {
		log.Error("创建充值订单失败: ", err)
		return nil, http.StatusInternalServerError, fmt.Errorf("创建充值订单失败: %v", err)
	}

	// 调用微信支付API创建支付订单
	wechatService := NewWechatService()
	result, err := wechatService.CreateWechatPayOrder(ctx, orderPay.ID, params.OpenID, orderPay.Price)
	if err != nil {
		// 更新本地订单状态为失败
		msg := "创建微信支付订单失败"
		_ = s.UpdatePayOrderState(orderPay.ID, models.StateFailed, "", msg, models.AddBalanceNone)
		return nil, http.StatusInternalServerError, fmt.Errorf("%s: %v", msg, err)
	}

	// 修改订单状态为支付中
	err = s.UpdatePayOrderState(orderPay.ID, models.StatePayment, "", "创建微信支付订单成功,等待用户支付", models.AddBalanceNone)
	if err != nil {
		return nil, http.StatusInternalServerError, fmt.Errorf("更新本地微信支付订单状态失败: %v", err)
	}

	// 返回结果
	return result, http.StatusOK, nil
}

// CreateWechatPayScoreOrderParams 创建微信支付分订单的参数结构体
type CreateWechatPayScoreOrderParams struct {
	UserID int64  // 用户ID
	Phone  string // 手机号码
	IP     string // IP地址
}

// CreateWechatPayScoreOrder 处理创建微信支付分订单的完整流程
func (s *PayService) CreateWechatPayScoreOrder(ctx *gin.Context, params CreateWechatPayScoreOrderParams) (*models.Pay, *core.APIResult, error) {
	// 创建微信支付分订单参数
	payParams := PayOrderParams{
		UserID:   params.UserID,
		Phone:    params.Phone, // 手机号码
		Name:     "微信支付分",
		ListID:   0, // 支付金额id(没有则填0)
		Price:    0, // 支付金额,支付分订单创建时没有
		Currency: 1, // 默认人民币
		IP:       params.IP,
		Type:     models.OrderTypeWechatPay, // 微信支付分
	}

	// 创建订单
	orderPay, err := s.CreatePayOrder(payParams)
	if err != nil {
		return nil, nil, fmt.Errorf("创建本地微信支付分订单记录失败: %v", err)
	}

	// 调用微信支付API创建支付分订单
	wechatService := NewWechatService()
	result, err := wechatService.CreateWechatPayScoreOrder(ctx, orderPay.ID)
	if err != nil {
		// 更新本地订单状态为失败
		msg := "创建微信支付分订单失败"
		_ = s.UpdatePayOrderState(orderPay.ID, models.StateFailed, "", msg, models.AddBalanceNone)
		return nil, nil, fmt.Errorf("%s: %v", msg, err)
	}

	// 更新本地订单状态为支付中
	err = s.UpdatePayOrderState(orderPay.ID, models.StatePayment, "", "创建微信支付分订单成功,等待订单结束", models.AddBalanceNone)
	if err != nil {
		return nil, nil, fmt.Errorf("更新本地微信支付分订单状态失败: %v", err)
	}

	return orderPay, result, nil
}

// HandleWechatNotify 处理微信支付回调
func (s *PayService) HandleWechatNotify(ctx *gin.Context) error {
	// 解析微信支付回调请求
	transaction, err := utils.DecryptNotify(ctx.Request)
	if err != nil {
		return errors.New("解密微信支付回调请求失败")
	}
	// 从请求直接获取解密后的数据 开发/测试用
	//transaction := &payments.Transaction{}
	//ctx.BindJSON(transaction)

	// 获取支付订单信息
	payOrder, err := s.GetPayOrderByID(*transaction.OutTradeNo, nil)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("订单不存在")
		}
		log.Error("获取支付订单失败: ", err)
		return errors.New("获取订单信息失败")
	}

	// 验证支付订单状态,防止重复回调
	if payOrder.State == models.StatePayComplete { // 已完成支付
		return nil
	}

	msg := TradeStateDescription[*transaction.TradeState]
	// 根据交易状态处理
	if *transaction.TradeState != TradeStateSuccess {
		// 支付失败处理
		err = s.UpdatePayOrderState(payOrder.ID, models.StateFailed, *transaction.TransactionId, msg, models.AddBalanceNone)
		if err != nil {
			log.Error("更新支付订单状态失败: ", err)
			return errors.New("更新支付订单状态失败")
		}
	}

	// 支付成功处理
	err = s.UpdatePayOrderState(payOrder.ID, models.StatePayComplete, *transaction.TransactionId, msg, models.AddBalanceSuccess)
	if err != nil {
		log.Error("处理支付成功失败: ", err)
		return errors.New("处理支付成功失败")
	}
	message := map[string]interface{}{
		"action": "pay_success",
		"data":   payOrder,
	}
	// 将支付订单推入队列
	err = utils.Publish(utils.PublishParams{
		Exchange:   "order",
		QueueName:  "order_pay",
		RoutingKey: "order.pay",
		Message:    message,
		Mode:       "direct",
		Durable:    true,
	})
	if err != nil {
		log.Error("发送支付订单到队列失败: ", err)
		return errors.New("发送支付订单到队列失败")
	}
	return nil
}

// PayOrderParams 创建支付订单的参数结构体
type PayOrderParams struct {
	UserID      int64  // 用户ID
	Phone       string // 手机号码
	Name        string // 订单名称
	ListID      int    // 价格列表ID（可选）
	Price       int64  // 支付金额
	Currency    int8   // 币种
	IP          string // IP地址
	Type        int8   // 订单类型：1-充值余额，3-微信支付分
	CustomPrice int64  // 自定义金额（可选）
}

// CreatePayOrder 通用创建订单
func (s *PayService) CreatePayOrder(params PayOrderParams) (*models.Pay, error) {
	var priceList *models.PriceList
	var err error

	// 如果有价格列表ID，获取价格列表信息
	if params.ListID != 0 {
		priceListService := NewPriceListService()
		priceList, err = priceListService.GetPriceListByID(params.ListID)
		if err != nil {
			log.Error("获取价格列表失败: ", err)
			return nil, err
		}

		// 使用价格列表中的信息
		params.Name = priceList.Title
		params.Price = priceList.Price
		params.Currency = priceList.Currency

		// 如果是自定义金额（ID为1的价格列表项）且传入了自定义金额，则使用自定义金额
		if params.ListID == 1 && params.CustomPrice > 0 {
			params.Price = params.CustomPrice
		}
	}

	orderType := ""
	switch params.Type {
	case models.OrderTypeRecharge:
		orderType = utils.OrderTypeRecharge
	case models.OrderTypeWechatPay:
		orderType = utils.OrderTypeWechatPay
	}

	// 生成订单号
	orderID, err := utils.GenerateOrderNo(database.DB.Model(&models.Pay{}), "id", orderType, params.UserID)
	if err != nil {
		log.Error("生成订单号失败: ", err)
		return nil, err
	}

	// 创建订单支付记录
	pay := &models.Pay{
		ID:          orderID,
		UserID:      params.UserID,
		Phone:       params.Phone, // 手机号码
		TradeNo:     nil,          // 外部订单编号
		Name:        params.Name,
		ListID:      params.ListID,
		Price:       params.Price,
		Currency:    params.Currency,
		State:       models.StateGenerateOrder, // 默认状态：生成订单
		IP:          params.IP,
		AddBalance:  models.AddBalanceNone, // 默认未加币
		Type:        params.Type,
		RefundState: models.RefundStateNone, // 默认无退款
	}

	// 保存到数据库
	err = pay.CreatePayOrder(database.DB)
	if err != nil {
		log.Error("创建订单失败: ", err)
		return nil, err
	}

	return pay, nil
}

// UpdatePayOrderState 更新支付订单的状态
func (s *PayService) UpdatePayOrderState(id string, state int8, tradeNo string, msg string, addBalance int8) error {
	pay := &models.Pay{}
	pay.ID = id
	pay.State = state
	pay.Msg = &msg
	pay.AddBalance = addBalance

	// 如果有交易号，则更新交易号
	if tradeNo != "" {
		pay.TradeNo = &tradeNo
	}
	return pay.UpdatePayOrder(database.DB, pay.ID)
}
