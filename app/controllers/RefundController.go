package controllers

import (
	"github.com/gin-gonic/gin"
	"resc-ms-order/app/models"
	"resc-ms-order/app/services"
	"resc-ms-order/app/utils"
	"resc-ms-order/router/middleware"
)

// RefundController 订单控制器结构体
type RefundController struct {
	BaseController // 嵌入基础控制器
	refundService  *services.RefundService
}

// NewRefundController 创建订单控制器实例
func NewRefundController() *RefundController {
	return &RefundController{
		refundService: services.NewRefundService(),
	}
}

// ListRefundOrdersRequest 查询参数结构体，用于接收查询参数
type ListRefundOrdersRequest struct {
	ID        string `form:"id"`
	OrderID   string `form:"order_id"`
	UserID    int64  `form:"user_id"`
	Phone     string `form:"phone"`
	State     string `form:"state"`
	StartTime string `form:"start_time"` // 开始时间
	EndTime   string `form:"end_time"`   // 结束时间
	Page      int    `form:"page"`       // 页码
	Limit     int    `form:"limit"`      // 每页数量
}

// ListRefundOrders 获取退款订单列表
func (r *RefundController) ListRefundOrders(ctx *gin.Context) {
	// 创建查询参数结构体
	var params ListRefundOrdersRequest

	// 绑定查询参数到结构体
	if err := ctx.ShouldBindQuery(&params); err != nil {
		r.BadRequest(ctx, utils.Translate(err))
		return
	}

	userOperator := "like"
	// 判断是管理员还是用户,如果是用户则筛选只能查看自己的订单
	if middleware.IsUser(ctx) {
		params.UserID = middleware.GetUserID(ctx)
		userOperator = "="
	}

	conditions := []utils.QueryCondition{
		{Field: "id", Operator: "like", Value: params.ID},
		{Field: "order_id", Operator: "like", Value: params.OrderID},
		{Field: "user_id", Operator: userOperator, Value: params.UserID},
		{Field: "phone", Operator: "like", Value: params.Phone},
		{Field: "state", Operator: "in", Value: params.State},
		{Field: "create_time", Operator: "between", Value: []string{params.StartTime, params.EndTime}},
	}

	// 调用服务层获取退款订单列表
	refunds, err := r.refundService.ListRefundOrders(params.Page, params.Limit, conditions)
	if err != nil {
		r.ServerError(ctx, "获取退款订单列表失败")
		return
	}
	count, _ := r.refundService.CountRefundOrders(nil)

	r.Success(ctx, gin.H{
		"list":  refunds,
		"count": count,
	})
}

// GetRefundOrderInfo 获取退款订单信息
func (r *RefundController) GetRefundOrderInfo(ctx *gin.Context) {
	type Request struct {
		Id      string `form:"id" binding:"required_without=OrderId"`
		OrderId string `form:"order_id" binding:"required_without=Id"`
		UserId  int64  `form:"user_id" binding:"-"`
	}
	request := Request{}
	// 绑定 JSON 数据到 request 结构体
	if err := ctx.ShouldBindQuery(&request); err != nil {
		r.BadRequest(ctx, utils.Translate(err))
		return
	}
	if middleware.IsUser(ctx) {
		request.UserId = middleware.GetUserID(ctx)
	}

	conditions := []utils.QueryCondition{
		{Field: "user_id", Operator: "=", Value: request.UserId},
	}
	var refund *models.Refund
	var err error

	if request.Id != "" {
		// 根据退款订单ID获取退款订单
		refund, err = r.refundService.GetRefundOrder(request.Id, conditions)
		if err != nil {
			r.NotFound(ctx, "退款订单信息不存在")
			return
		}
	} else {
		// 根据支付订单ID获取退款订单
		refund, err = r.refundService.GetRefundOrderByOrderID(request.OrderId, conditions)
		if err != nil {
			r.NotFound(ctx, "退款订单信息不存在")
			return
		}
	}

	// 返回退款订单信息
	r.Success(ctx, refund)
}

// RefundOrder 申请退款
func (r *RefundController) RefundOrder(ctx *gin.Context) {
	// 获取请求参数
	var request struct {
		OrderID string       `json:"order_id" binding:"required"` // 支付订单ID
		User    *models.User `json:"user" binding:"required"`
	}

	// 绑定JSON数据到request结构体
	if err := ctx.ShouldBindJSON(&request); err != nil {
		r.BadRequest(ctx, utils.Translate(err))
		return
	}
	request.User.UserID = request.User.ID

	// 调用服务层处理退款申请
	pay, err := r.refundService.RefundOrder(request.User, request.OrderID)
	if err != nil {
		r.BadRequest(ctx, err.Error())
		return
	}

	// 返回成功响应
	r.Success(ctx, pay.Price, "校验通过")
}

// CancelRefund 取消退款
func (r *RefundController) CancelRefund(ctx *gin.Context) {
	// 获取请求参数
	var request struct {
		RefundID string       `json:"refund_id" binding:"required"` // 退款订单ID
		User     *models.User `json:"user" binding:"required"`      // 用户信息
	}

	// 绑定JSON数据到request结构体
	if err := ctx.ShouldBindJSON(&request); err != nil {
		r.BadRequest(ctx, utils.Translate(err))
		return
	}
	request.User.UserID = request.User.ID

	// 调用服务层处理取消退款
	refundPrice, err := r.refundService.CancelRefund(request.RefundID, request.User)
	if err != nil {
		r.BadRequest(ctx, err.Error())
		return
	}

	// 返回成功响应
	r.Success(ctx, refundPrice.RefundPrice, "取消退款成功")
}

// RejectRefund 拒绝退款
func (r *RefundController) RejectRefund(ctx *gin.Context) {
	// 获取请求参数
	var request struct {
		RefundID string `json:"refund_id" binding:"required"` // 退款订单ID
	}

	// 绑定JSON数据到request结构体
	if err := ctx.ShouldBindJSON(&request); err != nil {
		r.BadRequest(ctx, utils.Translate(err))
		return
	}

	// 调用服务层处理拒绝退款
	refundOrder, err := r.refundService.RejectRefund(request.RefundID)
	if err != nil {
		r.BadRequest(ctx, err.Error())
		return
	}

	// 返回成功响应
	r.Success(ctx, refundOrder.RefundPrice, "拒绝退款成功")
}
