package consumers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"

	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
	"resc-ms-order/app/models"
	"resc-ms-order/app/services"
	"resc-ms-order/app/utils"
	"resc-ms-order/database"
)

// OrderConsumer 订单消费者结构体
type OrderConsumer struct {
	orderService *services.OrderService
}

// NewOrderConsumer 创建订单消费者实例
func NewOrderConsumer() *OrderConsumer {
	return &OrderConsumer{
		orderService: services.NewOrderService(),
	}
}

// Start 启动订单消费者
// 参数:
//   - ctx: 上下文，用于控制消费者停止
//
// 返回:
//   - error: 如果启动失败则返回错误
func (c *OrderConsumer) Start(ctx context.Context) error {
	// 配置消费者参数
	params := utils.ConsumeParams{
		Exchange:    "order",      // 交换机名称
		QueueName:   "order_sync", // 队列名称
		RoutingKey:  "order.sync", // 路由键
		Handler:     c.handleOrderMessage,
		Mode:        "direct", // 使用主题模式
		AutoRestart: true,     // 自动重启
		Context:     ctx,
	}

	return utils.Consume(params)
}

// OrderMessage 定义消息结构体
type OrderMessage struct {
	Action string                 `json:"action"` // 操作类型：create, update
	Data   map[string]interface{} `json:"data"`   // 订单数据
}

// handleOrderMessage 处理订单消息
// 消息格式: {"action": "create|update", "data": orderData}
func (c *OrderConsumer) handleOrderMessage(messageBody []byte) error {
	log.Debugf("收到订单消息: %s", string(messageBody))

	// 解析消息
	var orderMsg OrderMessage
	if err := json.Unmarshal(messageBody, &orderMsg); err != nil {
		log.Errorf("解析订单消息失败: %v", err)
		return fmt.Errorf("解析订单消息失败: %v", err)
	}

	// 根据操作类型处理消息
	switch orderMsg.Action {
	case "create":
		return c.handleCreateOrder(orderMsg.Data)
	case "update":
		return c.handleUpdateOrder(orderMsg.Data)
	case "refund":
		return c.handleRefundOrder(orderMsg.Data)
	default:
		log.Warnf("未知的操作类型: %s", orderMsg.Action)
		return fmt.Errorf("未知的操作类型: %s", orderMsg.Action)
	}
}

// handleCreateOrder 处理创建订单消息
func (c *OrderConsumer) handleCreateOrder(msg map[string]interface{}) error {
	// 将map数据转换为Order和OrderInfo结构体
	order, orderInfo, err := c.convertToOrderModels(msg)
	if err != nil {
		return fmt.Errorf("转换订单数据失败: %v", err)
	}

	// 创建订单和订单详情
	if err = c.orderService.CreateOrder(order, orderInfo); err != nil {
		log.Errorf("创建订单失败: %v", err)
		return fmt.Errorf("创建订单失败: %v", err)
	}

	return nil
}

// handleUpdateOrder 处理更新订单消息
func (c *OrderConsumer) handleUpdateOrder(msg map[string]interface{}) error {
	// 将map数据转换为Order和OrderInfo结构体
	order, orderInfo, err := c.convertToOrderModels(msg)
	if err != nil {
		return fmt.Errorf("转换订单数据失败: %v", err)
	}

	// 使用事务更新订单和订单详情
	err = database.DB.Transaction(func(tx *gorm.DB) error {
		// 更新订单
		if err := order.UpdateOrder(tx); err != nil {
			return fmt.Errorf("更新订单失败: %v", err)
		}

		// 更新订单详情
		orderInfo.OrderID = order.OrderID // 确保关联订单ID
		if err := orderInfo.UpdateOrderInfo(tx); err != nil {
			return fmt.Errorf("更新订单详情失败: %v", err)
		}

		return nil
	})

	if err != nil {
		log.Errorf("更新订单事务失败: %v", err)
		return fmt.Errorf("更新订单事务失败: %v", err)
	}
	return nil
}

type RefundMessage struct {
	OrderID string `json:"order_id"` // 支付订单id
	UserID  int64  `json:"user_id"`  // 用户id
	Price   int64  `json:"price"`    // 实际退款金额
	IP      string `json:"ip"`       // 用户ip
}

// handleRefundOrder 处理退款消息
func (c *OrderConsumer) handleRefundOrder(msg map[string]interface{}) error {
	// 将map数据转换为RefundMessage
	var data RefundMessage
	if err := c.mapToStruct(msg, &data); err != nil {
		return fmt.Errorf("转换退款消息失败: %v", err)
	}

	payService := services.NewPayService()
	payOrder, _ := payService.GetPayOrderByID(data.OrderID, nil)

	var refundID string
	// 开始数据库事务
	return database.DB.Transaction(func(tx *gorm.DB) error {
		// 1. 生成退款ID
		var err error
		refundID, err = utils.GenerateOrderNo(tx.Model(&models.Refund{}), "id", utils.OrderTypeRefund, data.UserID)
		if err != nil {
			log.Error("生成退款ID失败: ", err)
			return errors.New("生成退款ID失败")
		}

		// 3. 更新支付订单退款状态
		var payModel models.Pay
		allowedStates := []int8{models.RefundStateNone, models.RefundStateRejected, models.RefundStateCancelled}
		err = payModel.UpdateRefundState(tx, data.OrderID, data.UserID, models.RefundStateApplied, allowedStates)
		if err != nil {
			log.Error("更新支付订单退款状态失败: ", err)
			return errors.New("更新支付订单退款状态失败")
		}

		// 4. 创建退款订单记录
		refundOrder := &models.Refund{
			ID:               refundID,
			UserID:           data.UserID,
			Phone:            payOrder.Phone,
			OrderID:          data.OrderID,
			RefundPrice:      data.Price,
			OrderPrice:       payOrder.Price,
			Currency:         payOrder.Currency,
			OutTransactionID: payOrder.TradeNo,
			State:            models.RefundOrderStateApplied,
			IP:               data.IP,
			Reason:           "用户主动申请退款",
		}

		err = refundOrder.CreateRefundOrder(tx)
		if err != nil {
			log.Error("创建退款订单失败: ", err)
			return errors.New("创建退款订单失败")
		}

		return nil
	})
}

// convertToOrderModels 将map数据转换为Order和OrderInfo模型
// 这个方法处理从PHP端发送的数据格式转换
func (c *OrderConsumer) convertToOrderModels(data map[string]interface{}) (*models.Order, *models.OrderInfo, error) {
	// 创建Order和OrderInfo实例
	order := &models.Order{}
	orderInfo := &models.OrderInfo{}

	// 转换Order数据
	if err := c.mapToStruct(data, order); err != nil {
		return nil, nil, fmt.Errorf("转换Order数据失败: %v", err)
	}

	// 转换OrderInfo数据
	if err := c.mapToStruct(data, orderInfo); err != nil {
		return nil, nil, fmt.Errorf("转换OrderInfo数据失败: %v", err)
	}

	// 确保OrderInfo的OrderID与Order的OrderID一致
	if order.OrderID != "" {
		orderInfo.OrderID = order.OrderID
	}

	return order, orderInfo, nil
}

// mapToStruct 将map数据映射到结构体
// 使用JSON序列化和反序列化进行转换
func (c *OrderConsumer) mapToStruct(data map[string]interface{}, target interface{}) error {
	// 将map转换为JSON字节
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("序列化数据失败: %v", err)
	}

	// 将JSON字节反序列化到目标结构体
	if err := json.Unmarshal(jsonData, target); err != nil {
		return fmt.Errorf("反序列化到结构体失败: %v", err)
	}

	return nil
}
